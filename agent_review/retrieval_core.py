import os

import httpx
from dotenv import load_dotenv
from haystack import Document
from haystack.components.embedders import OpenAIDocumentEmbedder
from haystack.document_stores.types import DuplicatePolicy
from haystack_integrations.document_stores.opensearch import OpenSearchDocumentStore

load_dotenv(override=True)

doc_store = OpenSearchDocumentStore(
    hosts="https://opensearch.dev.kucoin.net",
    http_auth=("admin", "PMprbdrNw7KKJUqQQaWB"),
    index="redline",
    embedding_dim=1024,
)

docs = [
    Document(content="Machine learning is a subset of artificial intelligence."),
    Document(content="Deep learning is a subset of machine learning."),
    Document(content="Natural language processing is a field of AI."),
    Document(content="Reinforcement learning is a type of machine learning."),
    Document(content="Supervised learning is a type of machine learning."),
]

doc_embedder = OpenAIDocumentEmbedder(
    api_base_url=os.environ["EMBEDDING_BASE_URL"],
    api_key=os.environ["EMBEDDING_API_KEY"],
    model=os.environ["EMBEDDING_MODEL"],
    http_client_kwargs=httpx.Client(verify=False, timeout=30.0),
)
doc_embedder.warm_up()
docs = doc_embedder.run(docs)

doc_store.write_documents(docs["documents"], policy=DuplicatePolicy.OVERWRITE)
