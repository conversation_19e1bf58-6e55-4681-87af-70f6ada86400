"""
SSL配置管理模块
用于统一管理SSL证书验证设置
"""
import os
import warnings
import urllib3
from urllib3.exceptions import InsecureRequestWarning


class SSLConfig:
    """SSL配置管理类"""
    
    def __init__(self):
        # 从环境变量读取SSL配置，默认为安全设置
        self.verify_certs = os.getenv("SSL_VERIFY_CERTS", "true").lower() == "true"
        self.ssl_show_warn = os.getenv("SSL_SHOW_WARN", "true").lower() == "true"
        
    def configure_ssl_warnings(self):
        """配置SSL警告"""
        if not self.verify_certs and not self.ssl_show_warn:
            # 禁用SSL警告
            urllib3.disable_warnings(InsecureRequestWarning)
            warnings.filterwarnings("ignore", message="Unverified HTTPS request")
            warnings.filterwarnings("ignore", message="Connecting to .* using SSL with verify_certs=False is insecure")
    
    def get_opensearch_ssl_config(self):
        """获取OpenSearch SSL配置"""
        return {
            "verify_certs": self.verify_certs,
            "ssl_assert_hostname": self.verify_certs,
            "ssl_show_warn": self.ssl_show_warn,
        }
    
    def get_httpx_ssl_config(self):
        """获取HTTPX SSL配置"""
        return {
            "verify": self.verify_certs
        }


# 全局SSL配置实例
ssl_config = SSLConfig()

# 自动配置SSL警告
ssl_config.configure_ssl_warnings()
